<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Str;
use App\Services\SecurityVerificationService;
use App\Models\SecurityVerificationSession;
use SendGrid;
use SendGrid\Mail\Mail;

class SecurityVerificationController extends Controller
{
    protected $securityService;

    public function __construct(SecurityVerificationService $securityService)
    {
        $this->securityService = $securityService;
    }
    /**
     * Send security verification code to user's email
     */
    public function sendVerificationCode(Request $request)
    {
        try {
            $user = Auth::user();

            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'User not authenticated'
                ], 401);
            }

            // Check for rate limiting and attempt limits
            if ($this->securityService->hasExceededAttempts($user->id)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Too many verification attempts. Please try again later.'
                ], 429);
            }

            // Generate 6-digit verification code
            $verificationCode = $this->generateToken();

            // Create verification session in database
            $session = $this->securityService->createVerificationSession(
                $user->id,
                $user->email,
                $verificationCode
            );

            // Send verification email
            $emailSent = $this->sendSecurityVerificationEmail($user->email, $verificationCode);

            if (!$emailSent) {
                // Clean up database record if email failed
                $session->delete();
                throw new \Exception('Failed to send verification email');
            }

            \Log::info('Security verification code sent successfully', [
                'user_id' => $user->id,
                'session_id' => $session->session_id,
                'email' => $this->maskEmail($user->email)
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Verification code sent to your email',
                'session_id' => $session->session_id,
                'masked_email' => $this->maskEmail($user->email)
            ]);

        } catch (\Exception $e) {
            \Log::error('Failed to send security verification code', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to send verification code'
            ], 500);
        }
    }

    private function generateToken()
    {
        $characters = 'ACDEFGHJKMNPQRTUVWXYZ234679';
        return substr(str_shuffle(str_repeat($characters, 6)), 0, 6);
    }


    /**
     * Verify the security code and set secure cookie
     */
    public function verifyCode(Request $request)
    {
        $request->validate([
            'code' => 'required|string|size:6',
            'session_id' => 'required|string|uuid',
            'next' => 'nullable|string'
        ]);

        try {
            $user = Auth::user();

            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'User not authenticated'
                ], 401);
            }

            // Check for rate limiting
            if ($this->securityService->hasExceededAttempts($user->id)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Too many verification attempts. Please try again later.'
                ], 429);
            }

            // Get verification session from database
            $session = $this->securityService->getVerificationSession($request->session_id, $user->id);

            if (!$session || $session->isExpired()) {
                $this->securityService->recordFailedAttempt($user->id);
                return response()->json([
                    'success' => false,
                    'message' => 'Verification session expired or invalid'
                ], 400);
            }

            // Check verification attempts for this session
            if ($session->hasExceededMaxAttempts()) {
                $this->securityService->recordFailedAttempt($user->id);
                return response()->json([
                    'success' => false,
                    'message' => 'Too many failed attempts for this session'
                ], 400);
            }

            if ($session->verification_code !== $request->code) {
                // Increment attempts counter
                $session->incrementAttempts();

                $this->securityService->recordFailedAttempt($user->id);
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid verification code'
                ], 400);
            }

            // Clear failed attempts on successful verification
            $this->securityService->clearFailedAttempts($user->id);

            // Mark session as verified
            $session->markAsVerified();

            // Record first-time security verification if this is the user's first time
            if (!$user->first_security_verification_at) {
                $user->first_security_verification_at = now();
                $user->save();

                \Log::info('First-time security verification recorded', [
                    'user_id' => $user->id,
                    'verified_at' => $user->first_security_verification_at
                ]);
            }

            // Record first-time security verification if this is the user's first time
            if (!$user->first_security_verification_at) {
                $user->first_security_verification_at = now();
                $user->save();

                \Log::info('First-time security verification recorded', [
                    'user_id' => $user->id,
                    'verified_at' => $user->first_security_verification_at
                ]);
            }

            // Create secure cookie payload
            $cookiePayload = [
                'user_id' => $user->id,
                'verified_at' => now()->toISOString(),
                'session_id' => $request->session_id,
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent()
            ];

            // Generate secure cookie value with signing/encryption
            $cookieValue = $this->securityService->generateSecureCookieValue($cookiePayload);

            // Get dynamic cookie configuration
            $cookieConfig = $this->securityService->getCookieConfig();

            // Create secure cookie with dynamic configuration
            $cookie = Cookie::make(
                $cookieConfig['name'],
                $cookieValue,
                $cookieConfig['expires_minutes'],
                $cookieConfig['path'],
                $cookieConfig['domain'],
                $cookieConfig['secure'],
                $cookieConfig['http_only'],
                false, // raw
                $cookieConfig['same_site']
            );

            // Determine redirect URL with proper fallback
            $redirectUrl = $this->determineRedirectUrl($request);

            // Debug logging for redirect investigation
            \Log::info('Security verification redirect debugging', [
                'user_id' => $user->id,
                'session_id' => $request->session_id,
                'next_parameter_raw' => $request->input('next'),
                'next_parameter_exists' => $request->has('next'),
                'all_request_inputs' => $request->all(),
                'request_method' => $request->method(),
                'request_url' => $request->fullUrl(),
                'determined_redirect_url' => $redirectUrl
            ]);

            $response = response()->json([
                'success' => true,
                'message' => 'Security verification successful',
                'redirect_url' => $redirectUrl,
                'cookie_set' => true,
                'cookie_config' => [
                    'name' => $cookieConfig['name'],
                    'expires_in_minutes' => $cookieConfig['expires_minutes'],
                    'path' => $cookieConfig['path'],
                    'secure' => $cookieConfig['secure'],
                    'http_only' => $cookieConfig['http_only'],
                    'same_site' => $cookieConfig['same_site'],
                    'domain' => $cookieConfig['domain']
                ]
            ]);

            \Log::info('Security verification successful', [
                'user_id' => $user->id,
                'session_id' => $request->session_id,
                'redirect_url' => $redirectUrl,
                'cookie_domain' => $cookieConfig['domain'],
                'cookie_secure' => $cookieConfig['secure']
            ]);

            return $response->withCookie($cookie);

        } catch (\Exception $e) {
            \Log::error('Security verification failed', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id(),
                'session_id' => $request->session_id
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Verification failed'
            ], 500);
        }
    }

    /**
     * Resend verification code with enhanced rate limiting
     */
    public function resendCode(Request $request)
    {
        $request->validate([
            'session_id' => 'required|string|uuid'
        ]);

        try {
            $user = Auth::user();

            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'User not authenticated'
                ], 401);
            }

            // Check for rate limiting and attempt limits
            if ($this->securityService->hasExceededAttempts($user->id)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Too many verification attempts. Please try again later.'
                ], 429);
            }

            // Check rate limiting for resend requests
            $rateLimitSeconds = config('security.session.resend_rate_limit_seconds', 60);
            $rateLimitKey = "security_resend_{$user->id}";

            if (Cache::has($rateLimitKey)) {
                return response()->json([
                    'success' => false,
                    'message' => "Please wait {$rateLimitSeconds} seconds before requesting another code"
                ], 429);
            }

            // Set rate limit
            Cache::put($rateLimitKey, true, now()->addSeconds($rateLimitSeconds));

            // Get verification session from database
            $session = $this->securityService->getVerificationSession($request->session_id, $user->id);

            if (!$session) {
                return response()->json([
                    'success' => false,
                    'message' => 'Verification session expired'
                ], 400);
            }

            // Generate new code
            $newCode = $this->generateToken();

            // Update session with new code and reset attempts
            $this->securityService->updateVerificationCode($session, $newCode);

            // Send new verification email
            $emailSent = $this->sendSecurityVerificationEmail($user->email, $newCode);

            if (!$emailSent) {
                throw new \Exception('Failed to send verification email');
            }

            \Log::info('Security verification code resent successfully', [
                'user_id' => $user->id,
                'session_id' => $request->session_id
            ]);

            return response()->json([
                'success' => true,
                'message' => 'New verification code sent'
            ]);

        } catch (\Exception $e) {
            \Log::error('Failed to resend security verification code', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id(),
                'session_id' => $request->session_id
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to resend code'
            ], 500);
        }
    }

    /**
     * Send security verification email with comprehensive error handling
     */
    private function sendSecurityVerificationEmail($email, $code)
    {
        try {
            // Get SendGrid API key
            $apiKey = env('SENDGRID_API_KEY');
            if (!$apiKey) {
                \Log::error('SendGrid API key not configured');
                throw new \Exception('Email service not configured');
            }

            // Get template ID - use verification template from config
            $templateId = env('SENDGRID_VERIFICATION_CHECKUP_TEMPLATE_ID') ?: config('mail.templates.verification_token');
            if (!$templateId) {
                \Log::error('SendGrid template ID not configured');
                throw new \Exception('Email template not configured');
            }

            \Log::info('Attempting to send security verification email', [
                'email' => $this->maskEmail($email),
                'template_id' => $templateId,
                'code_length' => strlen($code)
            ]);

            // Initialize SendGrid
            $sendgrid = new SendGrid($apiKey, ['verify_ssl' => false]);

            // Create email message
            $message = new Mail();
            $message->setFrom(env('MAIL_FROM_ADDRESS', '<EMAIL>'), env('MAIL_FROM_NAME', 'TradeReply'));
            $message->setReplyTo(env('MAIL_TO_REPLY', '<EMAIL>'), 'Support Team');
            $message->setSubject("Security Verification Code");
            $message->addTo($email);
            $message->setTemplateId($templateId);

            // Add dynamic template data
            $message->addDynamicTemplateData('accountVerificationToken', $code);
            $message->addDynamicTemplateData('accountUsername', Auth::user()->username);
            $message->addDynamicTemplateData('accountEmail', Auth::user()->email);

            \Log::info('Sending email via SendGrid', [
                'to' => $this->maskEmail($email),
                'from' => env('MAIL_FROM_ADDRESS', '<EMAIL>'),
                'template_id' => $templateId
            ]);

            // Send email and check response
            $response = $sendgrid->send($message);

            \Log::info('SendGrid response received', [
                'status_code' => $response->statusCode(),
                'headers' => $response->headers(),
                'body' => $response->body()
            ]);

            // Check if email was sent successfully
            if ($response->statusCode() >= 200 && $response->statusCode() < 300) {
                \Log::info('Security verification email sent successfully', [
                    'email' => $this->maskEmail($email),
                    'status_code' => $response->statusCode()
                ]);
                return true;
            } else {
                \Log::error('SendGrid returned error status', [
                    'status_code' => $response->statusCode(),
                    'body' => $response->body(),
                    'email' => $this->maskEmail($email)
                ]);
                throw new \Exception('Email service returned error: ' . $response->statusCode());
            }

        } catch (\Exception $e) {
            \Log::error('Failed to send security verification email', [
                'error' => $e->getMessage(),
                'email' => $this->maskEmail($email),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Mask email address for display
     * Format: ** + last letter + domain (**<EMAIL>)
     * Matches frontend hashInput() implementation for consistency with signup flow
     */
    private function maskEmail($email)
    {
        if (!$email) {
            return "";
        }

        $parts = explode('@', $email);
        if (count($parts) !== 2) {
            return $email;
        }

        $username = $parts[0];
        $domain = $parts[1];

        if (strlen($username) <= 1) {
            return "**@" . $domain;
        }

        $lastChar = $username[strlen($username) - 1];
        return "**" . $lastChar . '@' . $domain;
    }

    /**
     * Determine the appropriate redirect URL after verification with enhanced debugging
     */
    private function determineRedirectUrl(Request $request): string
    {
        $nextUrl = $request->input('next');
        $referrer = $request->header('Referer');

        \Log::info('Determining redirect URL', [
            'next_parameter' => $nextUrl,
            'referrer_header' => $referrer,
            'all_request_data' => $request->all(),
            'request_headers' => $request->headers->all()
        ]);

        // Priority 1: Check 'next' parameter from request
        if ($nextUrl) {
            \Log::info('Found next parameter', ['next_url' => $nextUrl]);

            // Decode URL if it's encoded
            $decodedUrl = urldecode($nextUrl);
            \Log::info('Decoded next URL', ['decoded_url' => $decodedUrl]);

            // Validate the redirect URL for security
            if ($this->isValidRedirectUrl($decodedUrl)) {
                // Extract path from full URL for frontend compatibility
                $finalUrl = $this->extractPathFromUrl($decodedUrl);
                \Log::info('Next URL is valid, using it', [
                    'original_url' => $decodedUrl,
                    'final_redirect_url' => $finalUrl
                ]);
                return $finalUrl;
            } else {
                \Log::warning('Next URL failed validation', ['invalid_url' => $decodedUrl]);
            }
        }

        // Priority 2: Check for referrer in request headers
        if ($referrer && $this->isValidRedirectUrl($referrer)) {
            \Log::info('Using referrer header', ['referrer' => $referrer]);

            $parsedUrl = parse_url($referrer);
            if (isset($parsedUrl['path'])) {
                $path = $parsedUrl['path'];
                if (isset($parsedUrl['query'])) {
                    $path .= '?' . $parsedUrl['query'];
                }
                if (isset($parsedUrl['fragment'])) {
                    $path .= '#' . $parsedUrl['fragment'];
                }

                \Log::info('Constructed path from referrer', ['constructed_path' => $path]);
                return $path;
            }
        }

        // Priority 3: Default fallback
        \Log::info('Using default fallback redirect', ['default_url' => '/account/overview']);
        return '/account/overview';
    }




    /**
     * Validate redirect URL for security with enhanced logging
     */
    private function isValidRedirectUrl(string $url): bool
    {
        \Log::info('Validating redirect URL', ['url' => $url]);

        // Don't redirect back to security-check page
        if (str_contains($url, '/security-check')) {
            \Log::info('URL rejected: contains security-check', ['url' => $url]);
            return false;
        }

        // For relative URLs, allow them (frontend will handle route validation)
        if (!str_starts_with($url, 'http')) {
            \Log::info('Allowing relative URL', ['url' => $url]);
            return true;
        }

        // For absolute URLs, check if they're from allowed domains
        try {
            $parsedUrl = parse_url($url);
            $urlHost = $parsedUrl['host'] ?? null;

            if (!$urlHost) {
                \Log::info('URL accepted: no host specified', ['url' => $url]);
                return true;
            }

            // Get allowed domains from configuration
            $allowedDomains = $this->getAllowedRedirectDomains();

            \Log::info('Validating absolute URL', [
                'url' => $url,
                'parsed_host' => $urlHost,
                'allowed_domains' => $allowedDomains
            ]);

            // Check if the URL host is in the allowed domains list
            foreach ($allowedDomains as $allowedDomain) {
                if ($this->isDomainMatch($urlHost, $allowedDomain)) {
                    \Log::info('Absolute URL domain validation passed, allowing URL', [
                        'url' => $url,
                        'matched_domain' => $allowedDomain
                    ]);
                    return true;
                }
            }

            \Log::info('URL rejected: host not in allowed domains', [
                'url' => $url,
                'parsed_host' => $urlHost,
                'allowed_domains' => $allowedDomains
            ]);
            return false;

        } catch (\Exception $e) {
            \Log::error('URL validation failed with exception', [
                'url' => $url,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Get allowed domains for redirect URL validation
     */
    private function getAllowedRedirectDomains(): array
    {
        // Get current request host
        $currentHost = request()->getHost();

        // Base allowed domains
        $allowedDomains = [
            $currentHost, // Always allow current domain
        ];

        // Add environment-specific domains
        if (app()->environment('local')) {
            // Development domains
            $allowedDomains = array_merge($allowedDomains, [
                'localhost',
                '127.0.0.1',
                '::1',
            ]);
        } else {
            // Production domains
            $allowedDomains = array_merge($allowedDomains, [
                'tradereply.com',
                'dev.tradereply.com',
                'www.tradereply.com',
            ]);
        }

        return array_unique($allowedDomains);
    }

    /**
     * Check if a host matches an allowed domain pattern
     */
    private function isDomainMatch(string $host, string $allowedDomain): bool
    {
        // Exact match
        if ($host === $allowedDomain) {
            return true;
        }

        // Wildcard subdomain match (e.g., .tradereply.com matches dev.tradereply.com)
        if (str_starts_with($allowedDomain, '.') && str_ends_with($host, $allowedDomain)) {
            return true;
        }

        // Handle localhost variations (localhost:3000, localhost:8000, etc.)
        if (str_contains($allowedDomain, 'localhost') && str_contains($host, 'localhost')) {
            return true;
        }

        // Handle 127.0.0.1 variations
        if (str_contains($allowedDomain, '127.0.0.1') && str_contains($host, '127.0.0.1')) {
            return true;
        }

        return false;
    }

    /**
     * Extract path from URL for frontend compatibility
     */
    private function extractPathFromUrl(string $url): string
    {
        // If it's already a relative URL, return as is
        if (!str_starts_with($url, 'http')) {
            return $url;
        }

        try {
            $parsedUrl = parse_url($url);
            $path = $parsedUrl['path'] ?? '/';

            // Add query string if present
            if (isset($parsedUrl['query'])) {
                $path .= '?' . $parsedUrl['query'];
            }

            // Add fragment if present
            if (isset($parsedUrl['fragment'])) {
                $path .= '#' . $parsedUrl['fragment'];
            }

            \Log::info('Extracted path from URL', [
                'original_url' => $url,
                'extracted_path' => $path
            ]);

            return $path;
        } catch (\Exception $e) {
            \Log::warning('Failed to extract path from URL', [
                'url' => $url,
                'error' => $e->getMessage()
            ]);
            return $url; // Return original if parsing fails
        }
    }



    /**
     * Check if the current request has a valid security cookie
     */
    public function checkSecurityStatus(Request $request)
    {
        try {
            $cookieConfig = $this->securityService->getCookieConfig();
            $cookieValue = $request->cookie($cookieConfig['name']);

            if (!$cookieValue) {
                return response()->json([
                    'success' => false,
                    'verified' => false,
                    'message' => 'No security verification cookie found'
                ]);
            }

            $payload = $this->securityService->validateSecureCookieValue($cookieValue);

            if (!$payload) {
                return response()->json([
                    'success' => false,
                    'verified' => false,
                    'message' => 'Invalid or expired security verification'
                ]);
            }

            return response()->json([
                'success' => true,
                'verified' => true,
                'verified_at' => $payload['verified_at'] ?? null,
                'expires_in_minutes' => config('security.cookie.expires_minutes', 1)
            ]);

        } catch (\Exception $e) {
            \Log::error('Failed to check security status', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id()
            ]);

            return response()->json([
                'success' => false,
                'verified' => false,
                'message' => 'Failed to check security status'
            ], 500);
        }
    }

    /**
     * Test email functionality (for debugging purposes)
     */
    public function testEmail(Request $request)
    {
        $request->validate([
            'email' => 'required|email'
        ]);

        try {
            $testCode = '123456';
            $emailSent = $this->sendSecurityVerificationEmail($request->email, $testCode);

            return response()->json([
                'success' => $emailSent,
                'message' => $emailSent ? 'Test email sent successfully' : 'Failed to send test email',
                'email' => $this->maskEmail($request->email),
                'test_code' => $testCode
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Email test failed: ' . $e->getMessage(),
                'email' => $this->maskEmail($request->email)
            ], 500);
        }
    }

    /**
     * Debug redirect logic (for testing purposes)
     */
    public function debugRedirect(Request $request)
    {
        try {
            $redirectUrl = $this->determineRedirectUrl($request);

            return response()->json([
                'success' => true,
                'redirect_url' => $redirectUrl,
                'request_data' => $request->all(),
                'headers' => $request->headers->all(),
                'next_parameter' => $request->input('next'),
                'referrer' => $request->header('Referer')
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Debug failed: ' . $e->getMessage()
            ], 500);
        }
    }
}
