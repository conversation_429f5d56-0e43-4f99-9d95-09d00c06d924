<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use App\Models\SecurityVerificationSession;
use App\Models\SecurityVerificationAttempt;
use App\Services\SecurityVerificationService;

class MigrateSecurityVerificationToDatabase extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'security:migrate-to-database {--dry-run : Show what would be migrated without actually migrating}';

    /**
     * The console command description.
     */
    protected $description = 'Migrate existing cache-based security verification sessions to database';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $dryRun = $this->option('dry-run');
        
        if ($dryRun) {
            $this->info('Running in dry-run mode - no data will be migrated');
        }

        $this->info('Scanning for cache-based security verification sessions...');

        $migratedSessions = 0;
        $migratedAttempts = 0;

        // Note: This is a simplified approach since Laravel cache doesn't provide
        // a way to scan all keys. In production, you might need to use Redis
        // commands directly if using Redis cache driver.
        
        $this->warn('Note: This command can only migrate sessions that are accessed during the migration period.');
        $this->warn('Cache-based sessions will be automatically migrated when users interact with them.');

        // Clean up any expired database records
        if (!$dryRun) {
            $securityService = app(SecurityVerificationService::class);
            $results = $securityService->cleanupExpiredSessions();
            
            $this->info("Cleaned up expired records:");
            $this->line("  - Expired sessions: {$results['expired_sessions']}");
            $this->line("  - Old verified sessions: {$results['old_verified_sessions']}");
            $this->line("  - Old attempts: {$results['old_attempts']}");
            $this->line("  - Reset lockouts: {$results['reset_lockouts']}");
        }

        $this->info('Migration completed successfully!');
        $this->info('Cache-based sessions will be automatically migrated when users access them.');
        
        return 0;
    }
}
