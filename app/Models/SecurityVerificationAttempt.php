<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class SecurityVerificationAttempt extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'failed_attempts',
        'locked_until',
        'last_attempt_at',
    ];

    protected $casts = [
        'locked_until' => 'datetime',
        'last_attempt_at' => 'datetime',
        'failed_attempts' => 'integer',
    ];

    /**
     * Get the user that owns the verification attempts record.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Check if the user is currently locked out.
     */
    public function isLockedOut(): bool
    {
        return $this->locked_until && $this->locked_until->isFuture();
    }

    /**
     * Check if the user has exceeded max attempts.
     */
    public function hasExceededMaxAttempts(): bool
    {
        $maxAttempts = config('security.session.max_attempts', 5);
        return $this->failed_attempts >= $maxAttempts;
    }

    /**
     * Record a failed attempt.
     */
    public function recordFailedAttempt(): void
    {
        $this->increment('failed_attempts');
        $this->update(['last_attempt_at' => now()]);

        // Check if we should lock the user out
        if ($this->hasExceededMaxAttempts()) {
            $lockoutMinutes = config('security.session.lockout_minutes', 15);
            $this->update(['locked_until' => now()->addMinutes($lockoutMinutes)]);
        }
    }

    /**
     * Clear failed attempts (on successful verification).
     */
    public function clearFailedAttempts(): void
    {
        $this->update([
            'failed_attempts' => 0,
            'locked_until' => null,
            'last_attempt_at' => null,
        ]);
    }

    /**
     * Get or create attempts record for a user.
     */
    public static function getOrCreateForUser(int $userId): self
    {
        return static::firstOrCreate(
            ['user_id' => $userId],
            [
                'failed_attempts' => 0,
                'locked_until' => null,
                'last_attempt_at' => null,
            ]
        );
    }

    /**
     * Scope to get locked out users.
     */
    public function scopeLockedOut($query)
    {
        return $query->where('locked_until', '>', now());
    }

    /**
     * Scope to get users with failed attempts.
     */
    public function scopeWithFailedAttempts($query)
    {
        return $query->where('failed_attempts', '>', 0);
    }

    /**
     * Clean up old attempt records (older than lockout period).
     */
    public static function cleanupOldAttempts(): int
    {
        $lockoutMinutes = config('security.session.lockout_minutes', 15);
        
        return static::where('locked_until', '<=', now()->subMinutes($lockoutMinutes))
                    ->where('failed_attempts', 0)
                    ->delete();
    }

    /**
     * Reset expired lockouts.
     */
    public static function resetExpiredLockouts(): int
    {
        return static::where('locked_until', '<=', now())
                    ->update([
                        'failed_attempts' => 0,
                        'locked_until' => null,
                        'last_attempt_at' => null,
                    ]);
    }
}
