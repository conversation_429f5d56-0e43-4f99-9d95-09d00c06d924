<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class SecurityVerificationSession extends Model
{
    use HasFactory;

    protected $fillable = [
        'session_id',
        'user_id',
        'email',
        'verification_code',
        'expires_at',
        'verified',
        'verified_at',
        'attempts',
    ];

    protected $casts = [
        'expires_at' => 'datetime',
        'verified_at' => 'datetime',
        'verified' => 'boolean',
        'attempts' => 'integer',
    ];

    /**
     * Get the user that owns the verification session.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Check if the verification session has expired.
     */
    public function isExpired(): bool
    {
        return $this->expires_at->isPast();
    }

    /**
     * Check if the verification session is still valid.
     */
    public function isValid(): bool
    {
        return !$this->isExpired() && !$this->verified;
    }

    /**
     * Mark the session as verified.
     */
    public function markAsVerified(): void
    {
        $this->update([
            'verified' => true,
            'verified_at' => now(),
        ]);
    }

    /**
     * Increment the attempts counter.
     */
    public function incrementAttempts(): void
    {
        $this->increment('attempts');
    }

    /**
     * Reset the attempts counter.
     */
    public function resetAttempts(): void
    {
        $this->update(['attempts' => 0]);
    }

    /**
     * Check if max attempts have been exceeded.
     */
    public function hasExceededMaxAttempts(): bool
    {
        $maxAttempts = config('security.session.max_attempts', 5);
        return $this->attempts >= $maxAttempts;
    }

    /**
     * Scope to get active (non-expired, non-verified) sessions.
     */
    public function scopeActive($query)
    {
        return $query->where('verified', false)
                    ->where('expires_at', '>', now());
    }

    /**
     * Scope to get expired sessions.
     */
    public function scopeExpired($query)
    {
        return $query->where('expires_at', '<=', now());
    }

    /**
     * Scope to get verified sessions.
     */
    public function scopeVerified($query)
    {
        return $query->where('verified', true);
    }

    /**
     * Clean up expired verification sessions.
     */
    public static function cleanupExpired(): int
    {
        return static::expired()->delete();
    }

    /**
     * Clean up old verified sessions (older than 24 hours).
     */
    public static function cleanupOldVerified(): int
    {
        return static::verified()
                    ->where('verified_at', '<=', now()->subHours(24))
                    ->delete();
    }
}
