<?php

namespace App\Services;

use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Str;
use Carbon\Carbon;
use App\Models\SecurityVerificationSession;
use App\Models\SecurityVerificationAttempt;

class SecurityVerificationService
{
    /**
     * Generate a signed and optionally encrypted cookie value
     */
    public function generateSecureCookieValue(array $payload): string
    {
        // Add timestamp and nonce for security
        $payload['timestamp'] = now()->toISOString();
        $payload['nonce'] = Str::random(16);

        $jsonPayload = json_encode($payload);

        // Note: Encryption disabled for simplicity - using signing only

        // Always sign the payload for security
        $signature = $this->generateSignature($jsonPayload);
        $securePayload = [
            'data' => $jsonPayload,
            'signature' => $signature
        ];
        return base64_encode(json_encode($securePayload));
    }

    /**
     * Validate and decode a secure cookie value
     */
    public function validateSecureCookieValue(string $cookieValue): ?array
    {
        try {
            $decoded = json_decode(base64_decode($cookieValue), true);

            if (!$decoded) {
                return null;
            }

            // Verify signature (always required)
            if (!isset($decoded['data']) || !isset($decoded['signature'])) {
                return null;
            }

            // Verify signature
            if (!$this->verifySignature($decoded['data'], $decoded['signature'])) {
                return null;
            }

            $payload = $decoded['data'];

            // Note: Decryption not needed since encryption is disabled

            $data = json_decode($payload, true);

            if (!$data || !isset($data['timestamp'])) {
                return null;
            }

            // Check expiration
            $timestamp = Carbon::parse($data['timestamp']);
            $expiresMinutes = config('security.cookie.expires_minutes', 1);

            if ($timestamp->addMinutes($expiresMinutes)->isPast()) {
                return null;
            }

            return $data;

        } catch (\Exception $e) {
            \Log::warning('Failed to validate security cookie', [
                'error' => $e->getMessage(),
                'cookie_value' => substr($cookieValue, 0, 50) . '...'
            ]);
            return null;
        }
    }

    /**
     * Generate cryptographic signature for payload
     */
    private function generateSignature(string $payload): string
    {
        $key = config('app.key');
        $algorithm = config('security.crypto.signing_algorithm');

        return hash_hmac($algorithm, $payload, $key);
    }

    /**
     * Verify cryptographic signature
     */
    private function verifySignature(string $payload, string $signature): bool
    {
        $expectedSignature = $this->generateSignature($payload);
        return hash_equals($expectedSignature, $signature);
    }

    /**
     * Get dynamic cookie configuration based on environment
     */
    public function getCookieConfig(): array
    {
        $config = config('security.cookie');

        // Auto-detect domain and secure flag
        $config['domain'] = $this->detectCookieDomain();
        $config['secure'] = $this->shouldUseSecureCookies();

        return $config;
    }

    /**
     * Detect appropriate cookie domain based on current request
     */
    private function detectCookieDomain(): ?string
    {
        // Remove domain-specific logic to ensure consistent behavior across environments
        // Always return null to avoid domain-related cookie issues
        return null;
    }

    /**
     * Determine if secure cookies should be used
     */
    private function shouldUseSecureCookies(): bool
    {
        // Check if secure flag is explicitly set in config
        $configSecure = config('security.cookie.secure');
        if ($configSecure !== null) {
            return (bool) $configSecure;
        }

        if (!request()) {
            return false;
        }

        // Simplified logic: Use secure cookies for HTTPS, non-secure for HTTP
        // Remove environment-specific domain checking to ensure consistent behavior
        return request()->isSecure();
    }

    /**
     * Check if user has exceeded verification attempts
     * Includes fallback to cache for migration compatibility
     */
    public function hasExceededAttempts(int $userId): bool
    {
        $attemptRecord = SecurityVerificationAttempt::where('user_id', $userId)->first();

        if ($attemptRecord) {
            return $attemptRecord->isLockedOut() || $attemptRecord->hasExceededMaxAttempts();
        }

        // Fallback: Check cache for existing attempts during migration
        $cacheKey = "security_attempts_{$userId}";
        $cacheAttempts = cache()->get($cacheKey, 0);

        if ($cacheAttempts > 0) {
            // Migrate cache data to database
            $this->migrateCacheAttemptsToDatabase($userId, $cacheAttempts);

            // Clean up cache after migration
            cache()->forget($cacheKey);

            // Re-check with database record
            $attemptRecord = SecurityVerificationAttempt::where('user_id', $userId)->first();
            return $attemptRecord ? ($attemptRecord->isLockedOut() || $attemptRecord->hasExceededMaxAttempts()) : false;
        }

        return false;
    }

    /**
     * Record a failed verification attempt
     */
    public function recordFailedAttempt(int $userId): void
    {
        $attemptRecord = SecurityVerificationAttempt::getOrCreateForUser($userId);
        $attemptRecord->recordFailedAttempt();

        // Also clean up any cache-based attempts during migration
        $cacheKey = "security_attempts_{$userId}";
        cache()->forget($cacheKey);
    }

    /**
     * Clear failed attempts for user
     */
    public function clearFailedAttempts(int $userId): void
    {
        $attemptRecord = SecurityVerificationAttempt::where('user_id', $userId)->first();

        if ($attemptRecord) {
            $attemptRecord->clearFailedAttempts();
        }

        // Also clean up any cache-based attempts during migration
        $cacheKey = "security_attempts_{$userId}";
        cache()->forget($cacheKey);
    }

    /**
     * Migrate cache-based attempts to database
     */
    private function migrateCacheAttemptsToDatabase(int $userId, int $cacheAttempts): void
    {
        $lockoutMinutes = config('security.session.lockout_minutes', 15);
        $maxAttempts = config('security.session.max_attempts', 5);

        $lockedUntil = null;
        if ($cacheAttempts >= $maxAttempts) {
            $lockedUntil = now()->addMinutes($lockoutMinutes);
        }

        SecurityVerificationAttempt::create([
            'user_id' => $userId,
            'failed_attempts' => $cacheAttempts,
            'locked_until' => $lockedUntil,
            'last_attempt_at' => now(),
        ]);
    }

    /**
     * Create a new verification session
     */
    public function createVerificationSession(int $userId, string $email, string $verificationCode): SecurityVerificationSession
    {
        $sessionId = Str::uuid();
        $cacheExpires = config('security.session.cache_expires_minutes', 15);

        return SecurityVerificationSession::create([
            'session_id' => $sessionId,
            'user_id' => $userId,
            'email' => $email,
            'verification_code' => $verificationCode,
            'expires_at' => now()->addMinutes($cacheExpires),
            'verified' => false,
            'attempts' => 0,
        ]);
    }

    /**
     * Get verification session by session ID and user ID
     * Includes fallback to cache for migration compatibility
     */
    public function getVerificationSession(string $sessionId, int $userId): ?SecurityVerificationSession
    {
        // First try to get from database
        $session = SecurityVerificationSession::where('session_id', $sessionId)
                                             ->where('user_id', $userId)
                                             ->first();

        if ($session) {
            return $session;
        }

        // Fallback: Check cache for existing sessions during migration
        $cacheKey = "security_verification_{$userId}_{$sessionId}";
        $verificationData = cache()->get($cacheKey);

        if ($verificationData) {
            // Migrate cache data to database
            $session = $this->migrateCacheToDatabase($sessionId, $verificationData);

            // Clean up cache after migration
            cache()->forget($cacheKey);

            return $session;
        }

        return null;
    }

    /**
     * Migrate cache-based verification data to database
     */
    private function migrateCacheToDatabase(string $sessionId, array $cacheData): SecurityVerificationSession
    {
        $cacheExpires = config('security.session.cache_expires_minutes', 15);
        $createdAt = isset($cacheData['created_at']) ?
                    \Carbon\Carbon::parse($cacheData['created_at']) :
                    now();

        return SecurityVerificationSession::create([
            'session_id' => $sessionId,
            'user_id' => $cacheData['user_id'],
            'email' => $cacheData['email'],
            'verification_code' => $cacheData['code'],
            'expires_at' => $createdAt->addMinutes($cacheExpires),
            'verified' => $cacheData['verified'] ?? false,
            'verified_at' => isset($cacheData['verified_at']) ?
                           \Carbon\Carbon::parse($cacheData['verified_at']) :
                           null,
            'attempts' => $cacheData['attempts'] ?? 0,
            'created_at' => $createdAt,
            'updated_at' => now(),
        ]);
    }

    /**
     * Update verification session with new code (for resend)
     */
    public function updateVerificationCode(SecurityVerificationSession $session, string $newCode): void
    {
        $cacheExpires = config('security.session.cache_expires_minutes', 15);

        $session->update([
            'verification_code' => $newCode,
            'created_at' => now(),
            'attempts' => 0,
            'expires_at' => now()->addMinutes($cacheExpires),
        ]);
    }

    /**
     * Clean up expired verification sessions and attempts
     */
    public function cleanupExpiredSessions(): array
    {
        $expiredSessions = SecurityVerificationSession::cleanupExpired();
        $oldVerifiedSessions = SecurityVerificationSession::cleanupOldVerified();
        $oldAttempts = SecurityVerificationAttempt::cleanupOldAttempts();
        $resetLockouts = SecurityVerificationAttempt::resetExpiredLockouts();

        return [
            'expired_sessions' => $expiredSessions,
            'old_verified_sessions' => $oldVerifiedSessions,
            'old_attempts' => $oldAttempts,
            'reset_lockouts' => $resetLockouts,
        ];
    }
}
